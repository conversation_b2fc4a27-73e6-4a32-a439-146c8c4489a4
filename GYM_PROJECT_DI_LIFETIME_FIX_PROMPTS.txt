================================================================================
                    GYM PROJECT - DI LIFETIME CRITICAL FIX PROMPTS
================================================================================

🚨 KRİTİK SORUN RAPORU:
- Tüm DAL ve Service sınıfları SingleInstance() olarak kayıtlı
- Her DAL metodunda "using (new GymContext())" kullanılıyor
- 10.000+ kullanıcıda sistem çökecek durumda
- Memory leak, connection pool exhaustion, tenant data mixing riski
- Multi-tenant architecture için uygun değil

================================================================================
                                PROMPT 1
================================================================================
BAŞLIK: AutofacBusinessModule DI Lifetime Düzeltmesi (Acil Sistem Kurtarma)

AÇIKLAMA:
Tüm servisler SingleInstance() olarak kayıtlı. Bu multi-tenant ortamda veri
karışmasına neden oluyor. Önce DI lifetime'ları düzeltelim ki sistem çökmesin.

YAPILACAKLAR:
1. AutofacBusinessModule.cs'de tüm kayıtları InstancePerLifetimeScope() yap
2. Program.cs'de GymContext'i Scoped olarak kaydet
3. Sadece stateless servisler singleton kalacak

DOSYALAR:
- Business/DependencyResolvers/Autofac/AutofacBusinessModule.cs
- WebAPI/Program.cs

DETAY TALİMATLAR:
- Tüm Manager ve Dal sınıflarını .InstancePerLifetimeScope() yap
- JwtHelper, FileLoggerService, HttpContextAccessor singleton kalsın
- Program.cs'de containerBuilder.RegisterType<GymContext>().AsSelf().InstancePerLifetimeScope() ekle
- CompanyContext çakışmasını kontrol et (hem Autofac hem CoreModule'de var)

SONUÇ: Bu prompt sonrası proje çalışacak ama DAL'larda hala "using new" sorunu olacak.

================================================================================
                                PROMPT 2
================================================================================
BAŞLIK: Base Repository Constructor Düzeltmesi (Memory Leak Önleme)

AÇIKLAMA:
EfEntityRepositoryBase ve EfCompanyEntityRepositoryBase'de her metodda
"using (new TContext())" kullanılıyor. Constructor injection'a geçelim.

YAPILACAKLAR:
1. EfEntityRepositoryBase.cs - Constructor'da TContext injection ekle
2. EfCompanyEntityRepositoryBase.cs - Constructor'da TContext injection ekle
3. "using (new TContext())" kullanımlarını kaldır

DOSYALAR:
- Core/DataAccess/EntityFramework/EfEntityRepositoryBase.cs
- Core/DataAccess/EntityFramework/EfCompanyEntityRepositoryBase.cs

DETAY TALİMATLAR:
EfEntityRepositoryBase için:
- Constructor ekle: public EfEntityRepositoryBase(TContext context)
- Private readonly TContext _context field ekle
- Tüm metodlarda "using (new TContext())" yerine _context kullan

EfCompanyEntityRepositoryBase için:
- Constructor güncelle: public EfCompanyEntityRepositoryBase(TContext context, ICompanyContext companyContext)
- base(context) çağrısı ekle

SONUÇ: Bu prompt sonrası base class'lar hazır ama DAL'lar henüz constructor'ları yok.

================================================================================
                                PROMPT 3
================================================================================
BAŞLIK: Basit DAL Sınıflarına Constructor Ekleme

AÇIKLAMA:
EfEntityRepositoryBase kullanan DAL sınıflarına GymContext injection constructor'ı
ekleyeceğiz. Bu DAL'lar multi-tenant değil, sadece DbContext injection gerekli.

YAPILACAKLAR:
11 adet basit DAL sınıfına constructor ekle

DOSYALAR:
- DataAccess/Concrete/EntityFramework/EfUserDal.cs
- DataAccess/Concrete/EntityFramework/EfCompanyDal.cs
- DataAccess/Concrete/EntityFramework/EfCompanyUserDal.cs
- DataAccess/Concrete/EntityFramework/EfUserCompanyDal.cs
- DataAccess/Concrete/EntityFramework/EfCityDal.cs
- DataAccess/Concrete/EntityFramework/EfTownDal.cs
- DataAccess/Concrete/EntityFramework/EfOperationClaimDal.cs
- DataAccess/Concrete/EntityFramework/EfUserOperationClaimDal.cs
- DataAccess/Concrete/EntityFramework/EfUserLicenseDal.cs
- DataAccess/Concrete/EntityFramework/EfLicensePackageDal.cs
- DataAccess/Concrete/EntityFramework/EfCompanyAdressDal.cs

DETAY TALİMATLAR:
Her DAL için:
```csharp
public EfXxxDal(GymContext context) : base(context)
{
}
```
- Custom metodlardaki "using (GymContext context = new GymContext())" kaldır
- Injected context kullan

SONUÇ: Bu prompt sonrası basit DAL'lar çalışır durumda olacak.

================================================================================
                                PROMPT 4
================================================================================
BAŞLIK: Multi-Tenant DAL Sınıflarını Güncelleme

AÇIKLAMA:
EfCompanyEntityRepositoryBase kullanan DAL'lar zaten ICompanyContext var ama
constructor'ları GymContext almıyor. Güncelleyelim.

YAPILACAKLAR:
9 adet multi-tenant DAL'ı güncelle

DOSYALAR:
- DataAccess/Concrete/EntityFramework/EfMemberDal.cs
- DataAccess/Concrete/EntityFramework/EfMembershipDal.cs
- DataAccess/Concrete/EntityFramework/EfPaymentDal.cs
- DataAccess/Concrete/EntityFramework/EfExpenseDal.cs
- DataAccess/Concrete/EntityFramework/EfMembershipTypeDal.cs
- DataAccess/Concrete/EntityFramework/EfRemainingDebtDal.cs
- DataAccess/Concrete/EntityFramework/EfDebtPaymentDal.cs
- DataAccess/Concrete/EntityFramework/EfMemberWorkoutProgramDal.cs
- DataAccess/Concrete/EntityFramework/EfWorkoutProgramTemplateDal.cs

DETAY TALİMATLAR:
Her DAL için mevcut:
```csharp
public EfXxxDal(ICompanyContext companyContext) : base(companyContext)
```

Şu şekilde güncelle:
```csharp
public EfXxxDal(GymContext context, ICompanyContext companyContext) : base(context, companyContext)
```

- Custom metodlardaki "using (GymContext context = new GymContext())" kaldır
- Injected context kullan

SONUÇ: Bu prompt sonrası tüm DAL'lar DbContext injection kullanacak.

================================================================================
                                PROMPT 5
================================================================================
BAŞLIK: Test ve Doğrulama

AÇIKLAMA:
Tüm değişikliklerin test edilmesi ve sistem stabilitesinin kontrol edilmesi.

YAPILACAKLAR:
1. Projeyi derle ve hata kontrolü
2. Temel API endpoint'leri test et
3. Multi-tenant izolasyonu kontrol et

DETAY TALİMATLAR:
- dotnet build ile derleme kontrolü
- Swagger'da temel CRUD testleri
- Farklı CompanyId'lerle veri izolasyonu test et
- Memory usage gözlemle

SONUÇ: Sistem production'a hazır hale gelecek.

================================================================================
                            PROMPT DURUMU
================================================================================

✅ TAMAMLANAN PROMPTLAR:
(Henüz hiçbiri tamamlanmadı)

🔄 ŞU ANDA YAPILACAK PROMPT:
PROMPT 1 - AutofacBusinessModule DI Lifetime Düzeltmesi

⏳ BEKLEYEN PROMPTLAR:
- PROMPT 2 - Base Repository Constructor Düzeltmesi
- PROMPT 3 - Basit DAL Sınıflarına Constructor Ekleme
- PROMPT 4 - Multi-Tenant DAL Sınıflarını Güncelleme
- PROMPT 5 - Test ve Doğrulama

================================================================================
                              NOTLAR
================================================================================

⚠️  PROMPT SIRASI KRİTİK:
1 → 2 → 3 → 4 → 5 sırasıyla yapılmalı. Her prompt bir öncekine bağımlı.

🎯 HER PROMPT SONRASI:
- Proje derlenebilir durumda olacak
- Bir sonraki prompt'a geçilebilir
- Bu dosyayı güncelle ve tamamlanan prompt'u işaretle

🚨 ACİL UYARI:
Bu düzeltmeler production'a çıkmadan önce mutlaka yapılmalı!
